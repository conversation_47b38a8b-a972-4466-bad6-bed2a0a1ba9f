"use client";

import { Dictionary } from "../../lib/get-translation";
import { ApiResponse } from "../../types/market";
import { usePrimaryMarket } from "../../hooks/usePrimaryMarket";
import Button from "./variants/button";
import {
  ArgumentsSection,
  ArtistAuction,
  ArtistInfo,
  ArtistStats,
  ArtworkDetails,
  ArtworkImage,
  ArtworkSection,
  CertificateSection,
  DocumentLinks,
  PriceChart,
  PriceInformation,
  SharesInformation,
} from "../primary-market";
import { ArtistSection } from "../primary-market/ArtistSection";
import { ArtistIndexAndShare } from "../primary-market/ArtistIndexAndShare";

interface PrimaryMarketClientProps {
  dictionary: Dictionary;
  artworkData: ApiResponse;
  lang: "en" | "fr";
}

export default function PrimaryMarketClient({
  dictionary,
  artworkData,
  lang
}: PrimaryMarketClientProps) {
  // Early return if no paintings data
  if (!artworkData.paintings || artworkData.paintings.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 mb-4">
            {dictionary.nav.primary_market}
          </h1>
          <p className="text-gray-600">
            {lang === 'en'
              ? 'No artwork available in the primary market at the moment.'
              : 'Aucune œuvre disponible sur le marché primaire pour le moment.'
            }
          </p>
        </div>
      </div>
    );
  }

  // Get the first painting from the array
  const painting = artworkData.paintings[0];

  // Use the custom hook for market logic
  const {
    selectedShares,
    totalValue,
    isValidSelection,
  } = usePrimaryMarket(painting);

  const handleBuyClick = () => {
    if (isValidSelection) {
      console.log(`Buying ${selectedShares} shares for $${totalValue.toFixed(2)}`);
      alert(`Purchase initiated: ${selectedShares} shares for $${totalValue.toFixed(2)}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16 px-4">
          <h1
            className="font-prata font-normal text-gray-800 mb-2 mx-auto 
               text-xl sm:text-2xl md:text-3xl lg:text-4xl 
               leading-snug sm:leading-normal 
               whitespace-normal lg:whitespace-nowrap"
            style={{
              minHeight: '70px',
              letterSpacing: '0%',
              opacity: 1,
            }}
          >
            {dictionary.primary_market?.invest_in_work_by ||
              (lang === 'en' ? 'Invest in a Work by' : 'Investir dans une Œuvre de')}{' '}
            {painting.artist?.name || (lang === 'en' ? 'Artist' : 'Artiste')}
          </h1>
        </div>


        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 relative">
          {/* Left Column - Artwork Image */}
          <div className="flex flex-col items-center">
            <div className="relative bg-white p-4 rounded-sm shadow-inner mb-6">
              <ArtworkImage painting={painting} artworkData={artworkData} />
            </div>

            {/* Artist and Painting Information */}
            <ArtworkDetails painting={painting} lang={lang} />
          </div>

          {/* Responsive Separator Line - Only visible on large screens */}
          <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-px bg-black bg-opacity-20 transform -translate-x-1/2"></div>

          {/* Right Column - Investment Interface */}
          <div className="space-y-6">

            {/* Artist Info */}
            <div className="mb-8">
              <ArtistInfo artist={painting.artist} year={painting.year} />
            </div>

            {/* Price Information */}
            <PriceInformation painting={painting} dictionary={dictionary} lang={lang} />


            {/* Price Chart */}
            <PriceChart evolutionList={painting.evolution_list} />

            {/* Shares Information */}
            <SharesInformation painting={painting} dictionary={dictionary} lang={lang} />
            {/* Buy Button */}
            <Button
              onClick={handleBuyClick}
              //disabled={!isValidSelection}
              variant="primary"
              className="w-full"
            >
              {dictionary.user?.buy || (lang === 'en' ? 'BUY' : 'ACHETER')}
            </Button>

            {/* Action Icons */}
            <DocumentLinks painting={painting} artworkData={artworkData} lang={lang} />
          </div>
        </div>



        {/* Certificate and Arguments Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mt-20 relative">
          {/* Certificate Section */}
          <CertificateSection painting={painting} artworkData={artworkData} lang={lang} />

          {/* Responsive Separator Line - Only visible on large screens */}
          <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-px bg-black bg-opacity-20 transform -translate-x-1/2"></div>

          {/* Arguments Section */}
          <ArgumentsSection painting={painting} lang={lang} />
        </div>
        {/* Artwork Section */}
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-16 mt-20 relative">
          <ArtworkSection painting={painting} lang={lang} />
        </div>
        {/* Arguments Section */}
        <div className="">
          <ArtistSection painting={painting} lang={lang} />
        </div>
        {/* Indice Cotation Artiste & Cote Part Artiste */}
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-16 mt-20 relative">
          <ArtistIndexAndShare painting={painting} lang={lang} />
        </div>
        {/* Artist Statistics */}
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-16 mt-20 relative">
          <ArtistStats painting={painting} lang={lang} />
        </div>
        {/* Artist Auction artwork */}
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-16 mt-20 relative">
          <ArtistAuction painting={painting} lang={lang} />
        </div>
      </div>
    </div>
  );
}
